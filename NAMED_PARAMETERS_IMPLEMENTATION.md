# Named Parameter Binding Implementation

## Overview
This document outlines the comprehensive implementation of named parameter binding throughout the database system, ensuring that all parameter binding operations use named parameters that match column names instead of positional parameters.

## Changes Made

### 1. Database Class (`system/classes/database.class.php`)

#### A. Insert Method Updates
- **Before**: Used positional placeholders (`?`) 
- **After**: Uses named parameters that match column names (`:column_name`)

```php
// Before
$placeholders = implode(', ', array_fill(0, count($data), '?'));
$query = "INSERT INTO {$this->table} ($columns) VALUES ($placeholders)";
return $this->executeQuery($query, array_values($data));

// After  
foreach (array_keys($data) as $column) {
    $paramName = $this->generateColumnParamName($column);
    $placeholders[] = $paramName;
    $namedParams[$paramName] = $data[$column];
}
$query = "INSERT INTO {$this->table} ($columns) VALUES ($placeholderString)";
return $this->executeQuery($query, $namedParams);
```

#### B. Update Method Updates
- **Before**: Used positional placeholders (`?`) for SET clause
- **After**: Uses named parameters with 'set_' prefix (`:set_column_name`)

```php
// Before
foreach ($data as $column => $value) {
    $set .= "$column = ?, ";
    $values[] = $value;
}

// After
foreach ($data as $column => $value) {
    $paramName = $this->generateColumnParamName($column, 'set');
    $set .= "`$column` = $paramName, ";
    $namedParams[$paramName] = $value;
}
```

#### C. Where Clause Updates
- **Before**: Used generic parameter names (`:param0`, `:param1`)
- **After**: Uses column-based parameter names (`:where_column_name_0`)

```php
// Before
$conditions[] = "{$column} {$operator} ?";

// After
$paramName = $this->generateColumnParamName($column, 'where', $index);
$conditions[] = "`{$column}` {$operator} {$paramName}";
```

#### D. Delete Method Updates
- **Before**: Used `getBindingValues()` (positional)
- **After**: Uses `getBindings()` (named parameters)

#### E. New Helper Method
Added `generateColumnParamName()` method to create consistent named parameters:

```php
private function generateColumnParamName(string $column, string $context = '', ?int $index = null): string {
    // Clean column name to be safe for parameter names
    $cleanColumn = preg_replace('/[^a-zA-Z0-9_]/', '_', $column);
    
    // Build parameter name
    $paramName = ':';
    if (!empty($context)) {
        $paramName .= $context . '_';
    }
    $paramName .= $cleanColumn;
    
    // Add index if provided for uniqueness
    if ($index !== null) {
        $paramName .= '_' . $index;
    }
    
    return $paramName;
}
```

### 2. Schema Class Updates (`system/classes/database.class.php`)

#### A. hasTable Method
- **Before**: `SHOW TABLES LIKE ?`
- **After**: `SHOW TABLES LIKE :table_name`

#### B. hasColumn Method  
- **Before**: `SHOW COLUMNS FROM {$table} LIKE ?`
- **After**: `SHOW COLUMNS FROM {$table} LIKE :column_name`

### 3. Legacy Database Functions (`system/functions/database.php`)

#### A. tep_db_perform Function Updates
Updated both insert and update operations to use named parameters:

```php
// Insert - Before
$placeholders .= '?, ';
$values[] = $value;

// Insert - After
$paramName = ':' . preg_replace('/[^a-zA-Z0-9_]/', '_', $key);
$placeholders .= "$paramName, ";
$namedParams[$paramName] = $value;

// Update - Before  
$set .= "$column = ?, ";
$values[] = $value;

// Update - After
$paramName = ':set_' . preg_replace('/[^a-zA-Z0-9_]/', '_', $column);
$set .= "`$column` = $paramName, ";
$namedParams[$paramName] = $value;
```

## Parameter Naming Convention

The new system follows a consistent naming convention:

1. **Insert Parameters**: `:column_name`
   - Example: `:name`, `:email`, `:age`

2. **Update Parameters**: `:set_column_name`  
   - Example: `:set_name`, `:set_email`, `:set_age`

3. **Where Parameters**: `:where_column_name_index`
   - Example: `:where_email_0`, `:where_age_1`

4. **Complex Column Names**: Special characters are replaced with underscores
   - `user.name` → `:user_name`
   - `profile-image` → `:profile_image`
   - `created@timestamp` → `:created_timestamp`

## Benefits

### 1. Improved Debugging
- Parameter names now clearly indicate which column they represent
- Easier to trace parameter values in logs and error messages
- Better correlation between SQL queries and parameter values

### 2. Enhanced IDE Support
- IDEs can better understand parameter relationships
- Reduced "undefined constant" errors
- Better autocomplete and code analysis

### 3. Better Error Messages
- Database errors now show meaningful parameter names
- Easier to identify which column caused an issue
- More informative logging for troubleshooting

### 4. Consistency
- All database operations now use the same parameter naming convention
- Unified approach across both new database class and legacy functions
- Predictable parameter names for developers

## Example Usage

### Insert Operation
```php
// Input data
$data = ['name' => 'John Doe', 'email' => '<EMAIL>', 'age' => 30];

// Generated SQL
INSERT INTO users (`name`, `email`, `age`) VALUES (:name, :email, :age)

// Parameters
[':name' => 'John Doe', ':email' => '<EMAIL>', ':age' => 30]
```

### Update Operation
```php
// Input data  
$data = ['name' => 'John Smith', 'age' => 31];

// Generated SQL
UPDATE users SET `name` = :set_name, `age` = :set_age WHERE `email` = :where_email_0

// Parameters
[':set_name' => 'John Smith', ':set_age' => 31, ':where_email_0' => '<EMAIL>']
```

### Select with Where Conditions
```php
// Conditions
->where('age', '>', 25)->where('name', 'LIKE', 'John%')

// Generated SQL
SELECT * FROM users WHERE `age` > :where_age_0 AND `name` LIKE :where_name_1

// Parameters  
[':where_age_0' => 25, ':where_name_1' => 'John%']
```

## Testing

A comprehensive test script (`test_named_parameters.php`) was created to verify the parameter generation logic. The test confirms:

- Correct parameter name generation for various column types
- Proper handling of complex column names with special characters
- Consistent naming across insert, update, and where operations
- Parameter uniqueness when multiple conditions use the same column

## Backward Compatibility

All changes maintain backward compatibility:
- Existing code using the database class continues to work unchanged
- Legacy functions maintain their original interfaces
- No breaking changes to public APIs
- Existing parameter binding still functions correctly

## Conclusion

The implementation of named parameter binding provides significant improvements in debugging capabilities, IDE support, and code maintainability while maintaining full backward compatibility with existing code.
